# Test Environment Configuration
DATABASE_URL=sqlite:///:memory:
ENVIRONMENT=test
SECRET_KEY=test_secret_key_for_testing_only
JWT_SECRET_KEY=test_jwt_secret_key_for_testing_only
ANTHROPIC_API_KEY=test_anthropic_key
OPENAI_API_KEY=test_openai_key

# Test Database Settings
TEST_DATABASE_URL=sqlite:///:memory:

# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=CertRats Test Environment

# Security Settings
ACCESS_TOKEN_EXPIRE_MINUTES=30
ALGORITHM=HS256

# Email Settings (for testing)
SMTP_TLS=True
SMTP_PORT=587
SMTP_HOST=localhost
SMTP_USER=<EMAIL>
SMTP_PASSWORD=test_password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=CertRats Test

# Frontend Settings
NEXT_PUBLIC_API_URL=http://localhost:3001/api/v1
REACT_APP_API_URL=http://localhost:3001/api/v1

# Testing Flags
TESTING=true
DEBUG=true
