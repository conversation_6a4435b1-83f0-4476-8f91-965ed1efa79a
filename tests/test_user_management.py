"""
Tests for user management feature
"""
import pytest
from fastapi.testclient import <PERSON><PERSON><PERSON>
from api.app import app
from unittest.mock import patch, MagicMock
from models.user_management import UserRole, UserStatus
from models.user import User
import jwt
from datetime import datetime, timedelta

client = TestClient(app)

# Mock JWT token for testing
def create_test_token(username="testuser", role=UserRole.ADMIN):
    """Create a test JWT token"""
    payload = {
        "sub": username,
        "exp": datetime.utcnow() + timedelta(minutes=30)
    }
    return jwt.encode(payload, "your-secret-key", algorithm="HS256")

# Test data
test_user_data = {
    "username": "testuser",
    "email": "<EMAIL>",
    "full_name": "Test User",
    "password": "password123",
    "role": UserRole.USER,
    "status": UserStatus.ACTIVE
}

test_admin_data = {
    "username": "adminuser",
    "email": "<EMAIL>",
    "full_name": "Admin User",
    "password": "adminpass123",
    "role": UserRole.ADMIN,
    "status": UserStatus.ACTIVE
}

# Mock user objects
def create_mock_user(user_data, user_id=1):
    """Create a mock user object"""
    user = MagicMock()
    user.id = user_id
    user.username = user_data["username"]
    user.email = user_data["email"]
    user.full_name = user_data["full_name"]
    user.role = user_data["role"]
    user.status = user_data["status"]
    user.created_at = datetime.utcnow()
    user.updated_at = datetime.utcnow()
    user.last_login = None
    return user

# Tests for authentication
@patch('api.endpoints.user_management.get_db')
def test_login_success(mock_get_db):
    """Test successful login"""
    # Mock database session
    mock_session = MagicMock()
    mock_user = create_mock_user(test_user_data)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    # Mock password verification
    with patch('api.endpoints.user_management.verify_password', return_value=True):
        response = client.post(
            "/users/token",
            data={"username": "testuser", "password": "password123"}
        )
        
        assert response.status_code == 200
        assert "access_token" in response.json()
        assert response.json()["token_type"] == "bearer"

@patch('api.endpoints.user_management.get_db')
def test_login_invalid_credentials(mock_get_db):
    """Test login with invalid credentials"""
    # Mock database session
    mock_session = MagicMock()
    mock_user = create_mock_user(test_user_data)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    # Mock password verification
    with patch('api.endpoints.user_management.verify_password', return_value=False):
        response = client.post(
            "/users/token",
            data={"username": "testuser", "password": "wrongpassword"}
        )
        
        assert response.status_code == 401
        assert "Incorrect username or password" in response.json()["detail"]

@patch('api.endpoints.user_management.get_db')
def test_login_inactive_user(mock_get_db):
    """Test login with inactive user"""
    # Create inactive user
    inactive_user_data = test_user_data.copy()
    inactive_user_data["status"] = UserStatus.INACTIVE
    
    # Mock database session
    mock_session = MagicMock()
    mock_user = create_mock_user(inactive_user_data)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    # Mock password verification
    with patch('api.endpoints.user_management.verify_password', return_value=True):
        response = client.post(
            "/users/token",
            data={"username": "testuser", "password": "password123"}
        )
        
        assert response.status_code == 401
        assert "User account is not active" in response.json()["detail"]

# Tests for user management endpoints
@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_get_users(mock_get_db, mock_get_current_user):
    """Test getting all users"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    mock_users = [create_mock_user(test_user_data, 1), create_mock_user(test_admin_data, 2)]
    mock_session.query.return_value.filter.return_value.offset.return_value.limit.return_value.all.return_value = mock_users
    mock_session.query.return_value.count.return_value = len(mock_users)
    mock_get_db.return_value = mock_session
    
    response = client.get("/users/")
    
    assert response.status_code == 200
    assert "users" in response.json()
    assert "total" in response.json()
    assert response.json()["total"] == 2
    assert len(response.json()["users"]) == 2

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_create_user(mock_get_db, mock_get_current_user):
    """Test creating a new user"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    mock_session.query.return_value.filter.return_value.first.return_value = None  # No existing user
    mock_get_db.return_value = mock_session
    
    # Mock password hashing
    with patch('api.endpoints.user_management.get_password_hash', return_value="hashed_password"):
        response = client.post("/users/", json=test_user_data)
        
        assert response.status_code == 201
        assert response.json()["username"] == test_user_data["username"]
        assert response.json()["email"] == test_user_data["email"]
        assert "password" not in response.json()  # Password should not be returned

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_create_user_existing_username(mock_get_db, mock_get_current_user):
    """Test creating a user with existing username"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    mock_session.query.return_value.filter.return_value.first.return_value = create_mock_user(test_user_data)  # Existing user
    mock_get_db.return_value = mock_session
    
    response = client.post("/users/", json=test_user_data)
    
    assert response.status_code == 400
    assert "Username or email already registered" in response.json()["detail"]

@patch('api.endpoints.user_management.get_current_user')
def test_get_current_user_info(mock_get_current_user):
    """Test getting current user info"""
    # Mock current user
    mock_user = create_mock_user(test_user_data)
    mock_get_current_user.return_value = mock_user
    
    response = client.get("/users/me")
    
    assert response.status_code == 200
    assert response.json()["username"] == test_user_data["username"]
    assert response.json()["email"] == test_user_data["email"]

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_update_current_user(mock_get_db, mock_get_current_user):
    """Test updating current user"""
    # Mock current user
    mock_user = create_mock_user(test_user_data)
    mock_get_current_user.return_value = mock_user
    
    # Mock database session
    mock_session = MagicMock()
    mock_session.query.return_value.filter.return_value.first.return_value = None  # No existing user with new username
    mock_get_db.return_value = mock_session
    
    # Update data
    update_data = {
        "full_name": "Updated Name",
        "email": "<EMAIL>"
    }
    
    response = client.put("/users/me", json=update_data)
    
    assert response.status_code == 200
    assert response.json()["full_name"] == update_data["full_name"]
    assert response.json()["email"] == update_data["email"]

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_get_user_by_id(mock_get_db, mock_get_current_user):
    """Test getting a user by ID"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    mock_user = create_mock_user(test_user_data)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    response = client.get("/users/1")
    
    assert response.status_code == 200
    assert response.json()["username"] == test_user_data["username"]
    assert response.json()["email"] == test_user_data["email"]

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_update_user(mock_get_db, mock_get_current_user):
    """Test updating a user"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    mock_user = create_mock_user(test_user_data)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    # Update data
    update_data = {
        "role": UserRole.ADMIN,
        "status": UserStatus.INACTIVE
    }
    
    response = client.put("/users/1", json=update_data)
    
    assert response.status_code == 200
    assert response.json()["role"] == update_data["role"]
    assert response.json()["status"] == update_data["status"]

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_delete_user(mock_get_db, mock_get_current_user):
    """Test deleting a user"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data, user_id=2)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    mock_user = create_mock_user(test_user_data, user_id=1)
    mock_session.query.return_value.filter.return_value.first.return_value = mock_user
    mock_get_db.return_value = mock_session
    
    response = client.delete("/users/1")
    
    assert response.status_code == 204

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_delete_self(mock_get_db, mock_get_current_user):
    """Test deleting own account"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data, user_id=1)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    mock_session.query.return_value.filter.return_value.first.return_value = mock_admin
    mock_get_db.return_value = mock_session
    
    response = client.delete("/users/1")
    
    assert response.status_code == 400
    assert "Cannot delete your own account" in response.json()["detail"]

@patch('api.endpoints.user_management.get_current_user')
@patch('api.endpoints.user_management.get_db')
def test_get_user_stats(mock_get_db, mock_get_current_user):
    """Test getting user statistics"""
    # Mock current user (admin)
    mock_admin = create_mock_user(test_admin_data)
    mock_get_current_user.return_value = mock_admin
    
    # Mock database session
    mock_session = MagicMock()
    
    # Mock count queries
    mock_session.query.return_value.scalar.return_value = 10  # Total users
    mock_session.query.return_value.filter.return_value.scalar.return_value = 8  # Active users
    
    # Mock role counts
    mock_session.query.return_value.group_by.return_value.all.side_effect = [
        [(UserRole.ADMIN, 2), (UserRole.USER, 8)],  # Role counts
        [(UserStatus.ACTIVE, 8), (UserStatus.INACTIVE, 2)]  # Status counts
    ]
    
    mock_get_db.return_value = mock_session
    
    response = client.get("/users/stats")
    
    assert response.status_code == 200
    assert response.json()["total_users"] == 10
    assert response.json()["active_users"] == 8
    assert UserRole.ADMIN in response.json()["users_by_role"]
    assert UserRole.USER in response.json()["users_by_role"]
    assert UserStatus.ACTIVE in response.json()["users_by_status"]
    assert UserStatus.INACTIVE in response.json()["users_by_status"]
