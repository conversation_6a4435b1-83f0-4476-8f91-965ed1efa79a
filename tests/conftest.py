"""Test configuration and fixtures"""
from api.app import app
from database import Base, get_db
import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, Session
from fastapi.testclient import TestClient
import sys
import os
from pathlib import Path

# Import all models to ensure they're registered with Base
from models.user import User, UserPreference, UserActivity
from models.certification import Certification, Organization
from models.reports import Report

# Add project root to Python path
project_root = str(Path(__file__).parent.parent)
if project_root not in sys.path:
    sys.path.append(project_root)


@pytest.fixture(scope="session")
def db_engine():
    """Create a test database engine"""
    # Use a test database URL or in-memory SQLite for testing with proper threading
    database_url = os.getenv("TEST_DATABASE_URL", "sqlite:///:memory:")
    engine = create_engine(
        database_url,
        # Allow SQLite to be used across threads
        connect_args={"check_same_thread": False},
        poolclass=None  # Disable connection pooling for tests
    )
    Base.metadata.create_all(engine)
    return engine


@pytest.fixture(scope="function")
def db_session(db_engine):
    """Create a new database session for a test"""
    connection = db_engine.connect()
    transaction = connection.begin()
    session = sessionmaker(bind=connection)()

    yield session

    session.close()
    transaction.rollback()
    connection.close()


@pytest.fixture(scope="function")
def test_client(db_session):
    """Create a test client with database override"""
    def override_get_db():
        try:
            yield db_session
        finally:
            pass

    app.dependency_overrides[get_db] = override_get_db
    client = TestClient(app)
    yield client
    app.dependency_overrides.clear()
