"""
API endpoints for user management feature
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Path, Body, status
from fastapi.security import OAuth2Password<PERSON>earer, OAuth2PasswordRequestForm
from typing import List, Optional, Dict, Any
from models.user_management import (
    UserBase,
    UserCreate,
    UserUpdate,
    UserResponse,
    UserPreference,
    UserActivity,
    UserStats,
    UserListResponse,
    UserRole,
    UserStatus
)
from models.user import User, UserPreference as UserPreferenceModel, UserActivity as UserActivityModel
from database import get_db
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, select, and_, or_
from datetime import datetime, timedelta
import bcrypt
import jwt
from jwt.exceptions import PyJWTError
import logging
from models.certification import UserCertification, Certification

# Configure logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(
    tags=["users"],
    responses={404: {"description": "User not found"}},
)

# JWT settings
SECRET_KEY = "your-secure-jwt-key"  # In production, use environment variable
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

# OAuth2 scheme
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="users/token")

# Helper functions


def get_password_hash(password: str) -> str:
    """Hash a password for storing"""
    salt = bcrypt.gensalt()
    hashed_password = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed_password.decode('utf-8')


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a stored password against a provided password"""
    return bcrypt.checkpw(plain_password.encode('utf-8'), hashed_password.encode('utf-8'))


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Create a JWT token"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt


async def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)):
    """Get the current user from the token"""
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            raise credentials_exception
    except PyJWTError:
        raise credentials_exception

    # Get user from database
    user = db.query(User).filter(User.username == username).first()
    if user is None:
        raise credentials_exception
    return user


async def get_current_active_user(current_user=Depends(get_current_user)):
    """Check if the current user is active"""
    if current_user.status != UserStatus.ACTIVE:
        raise HTTPException(status_code=400, detail="Inactive user")
    return current_user


async def get_admin_user(current_user=Depends(get_current_active_user)):
    """Check if the current user is an admin"""
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not enough permissions"
        )
    return current_user

# Authentication endpoints


@router.post("/token", response_model=Dict[str, str])
async def login_for_access_token(form_data: OAuth2PasswordRequestForm = Depends(), db: Session = Depends(get_db)):
    """Login endpoint to get JWT token"""
    # Find user by username
    user = db.query(User).filter(User.username == form_data.username).first()
    if not user or not verify_password(form_data.password, user.password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Check if user is active
    if user.status != UserStatus.ACTIVE:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is not active",
            headers={"WWW-Authenticate": "Bearer"},
        )

    # Create access token
    access_token_expires = timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    access_token = create_access_token(
        data={"sub": user.username}, expires_delta=access_token_expires
    )

    # Update last login time
    user.last_login = datetime.utcnow()
    db.commit()

    # Log user activity
    activity = UserActivity(
        user_id=user.id,
        action="login",
        details={"ip": "127.0.0.1"}  # In production, get real IP
    )
    db.add(activity)
    db.commit()

    return {"access_token": access_token, "token_type": "bearer"}

# User management endpoints


@router.get("/", response_model=UserListResponse)
async def get_users(
    skip: int = 0,
    limit: int = 10,
    role: Optional[UserRole] = None,
    status: Optional[UserStatus] = None,
    search: Optional[str] = None,
    current_user=Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get all users with pagination and filtering
    Only accessible by admin users
    """
    # Base query
    query = db.query(User)

    # Apply filters
    if role:
        query = query.filter(User.role == role)
    if status:
        query = query.filter(User.status == status)
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                User.username.ilike(search_term),
                User.email.ilike(search_term),
                User.full_name.ilike(search_term)
            )
        )

    # Get total count
    total = query.count()

    # Apply pagination
    users = query.offset(skip).limit(limit).all()

    return UserListResponse(
        users=users,
        total=total,
        page=skip // limit + 1,
        page_size=limit
    )


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate,
    current_user=Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Create a new user
    Only accessible by admin users
    """
    # Check if username or email already exists
    existing_user = db.query(User).filter(
        or_(
            User.username == user.username,
            User.email == user.email
        )
    ).first()

    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Username or email already registered"
        )

    # Hash the password
    hashed_password = get_password_hash(user.password)

    # Create new user
    db_user = User(
        username=user.username,
        email=user.email,
        full_name=user.full_name,
        role=user.role,
        status=user.status,
        password=hashed_password,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow()
    )

    db.add(db_user)
    db.commit()
    db.refresh(db_user)

    # Log activity
    activity = UserActivity(
        user_id=current_user.id,
        action="create_user",
        details={"created_user_id": db_user.id}
    )
    db.add(activity)
    db.commit()

    return db_user


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user=Depends(get_current_active_user)):
    """Get information about the current logged-in user"""
    return current_user


@router.put("/me", response_model=UserResponse)
async def update_current_user(
    user_update: UserUpdate,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update the current logged-in user"""
    # Update user fields
    if user_update.username is not None:
        # Check if username is already taken
        existing_user = db.query(User).filter(
            and_(
                User.username == user_update.username,
                User.id != current_user.id
            )
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
        current_user.username = user_update.username

    if user_update.email is not None:
        # Check if email is already taken
        existing_user = db.query(User).filter(
            and_(
                User.email == user_update.email,
                User.id != current_user.id
            )
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        current_user.email = user_update.email

    if user_update.full_name is not None:
        current_user.full_name = user_update.full_name

    if user_update.password is not None:
        current_user.password = get_password_hash(user_update.password)

    # Regular users cannot change their role or status

    current_user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(current_user)

    # Log activity
    activity = UserActivity(
        user_id=current_user.id,
        action="update_profile",
        details={}
    )
    db.add(activity)
    db.commit()

    return current_user


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: int = Path(..., gt=0),
    current_user=Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific user by ID
    Only accessible by admin users
    """
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return user


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_update: UserUpdate,
    user_id: int = Path(..., gt=0),
    current_user=Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Update a user
    Only accessible by admin users
    """
    # Get user
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Update user fields
    if user_update.username is not None:
        # Check if username is already taken
        existing_user = db.query(User).filter(
            and_(
                User.username == user_update.username,
                User.id != user_id
            )
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already taken"
            )
        user.username = user_update.username

    if user_update.email is not None:
        # Check if email is already taken
        existing_user = db.query(User).filter(
            and_(
                User.email == user_update.email,
                User.id != user_id
            )
        ).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
        user.email = user_update.email

    if user_update.full_name is not None:
        user.full_name = user_update.full_name

    if user_update.role is not None:
        user.role = user_update.role

    if user_update.status is not None:
        user.status = user_update.status

    if user_update.password is not None:
        user.password = get_password_hash(user_update.password)

    user.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(user)

    # Log activity
    activity = UserActivity(
        user_id=current_user.id,
        action="update_user",
        details={"updated_user_id": user_id}
    )
    db.add(activity)
    db.commit()

    return user


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: int = Path(..., gt=0),
    current_user=Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Delete a user
    Only accessible by admin users
    """
    # Get user
    user = db.query(User).filter(User.id == user_id).first()
    if user is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )

    # Cannot delete yourself
    if user.id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Cannot delete your own account"
        )

    # Delete user
    db.delete(user)
    db.commit()

    # Log activity
    activity = UserActivity(
        user_id=current_user.id,
        action="delete_user",
        details={"deleted_user_id": user_id}
    )
    db.add(activity)
    db.commit()

    return None


@router.get("/stats", response_model=UserStats)
async def get_user_stats(
    current_user=Depends(get_admin_user),
    db: Session = Depends(get_db)
):
    """
    Get user statistics
    Only accessible by admin users
    """
    # Total users
    total_users = db.query(func.count(User.id)).scalar()

    # Active users
    active_users = db.query(func.count(User.id)).filter(
        User.status == UserStatus.ACTIVE).scalar()

    # Users by role
    role_counts = db.query(
        User.role,
        func.count(User.id)
    ).group_by(User.role).all()
    users_by_role = {role: count for role, count in role_counts}

    # Users by status
    status_counts = db.query(
        User.status,
        func.count(User.id)
    ).group_by(User.status).all()
    users_by_status = {status: count for status, count in status_counts}

    # New users in last 30 days
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    new_users = db.query(func.count(User.id)).filter(
        User.created_at >= thirty_days_ago).scalar()

    return UserStats(
        total_users=total_users,
        active_users=active_users,
        users_by_role=users_by_role,
        users_by_status=users_by_status,
        new_users_last_30_days=new_users
    )

# Profile endpoint


@router.get("/profile", response_model=UserResponse)
async def get_user_profile(current_user=Depends(get_current_active_user)):
    """
    Get current user's profile

    Returns the profile information of the currently authenticated user.
    """
    logger.info(f"Profile request for user ID: {current_user.id}")
    try:
        return current_user
    except Exception as e:
        logger.error(f"Error retrieving profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving user profile"
        )


@router.put("/profile", response_model=UserResponse)
async def update_user_profile(
    profile_update: UserUpdate,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's profile

    Updates the profile information of the currently authenticated user.
    """
    logger.info(f"Profile update for user ID: {current_user.id}")
    try:
        # Update the user fields
        if profile_update.username is not None:
            # Check if username is already taken
            existing = db.query(User).filter(
                User.username == profile_update.username,
                User.id != current_user.id
            ).first()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Username already taken"
                )
            current_user.username = profile_update.username

        if profile_update.email is not None:
            # Check if email is already taken
            existing = db.query(User).filter(
                User.email == profile_update.email,
                User.id != current_user.id
            ).first()
            if existing:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already taken"
                )
            current_user.email = profile_update.email

        if profile_update.full_name is not None:
            current_user.full_name = profile_update.full_name

        if profile_update.password is not None:
            # Hash the new password
            from api.endpoints.auth import get_password_hash
            current_user.password = get_password_hash(profile_update.password)

        # Update the timestamp
        current_user.updated_at = datetime.utcnow()

        # Save to database
        db.commit()
        db.refresh(current_user)

        # Log activity
        try:
            activity = UserActivityModel(
                user_id=current_user.id,
                action="profile_update",
                details={"fields_updated": [k for k, v in profile_update.dict(
                    exclude_unset=True).items() if v is not None]}
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.warning(f"Failed to log profile update activity: {str(e)}")
            # Continue even if logging fails

        logger.info(
            f"Profile updated successfully for user ID: {current_user.id}")
        return current_user
    except HTTPException:
        # Re-raise HTTP exceptions to preserve status code and details
        raise
    except Exception as e:
        logger.error(f"Error updating profile: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating user profile"
        )

# User preferences endpoints


@router.get("/preferences", response_model=UserPreference)
async def get_user_preferences(
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get current user's preferences

    Returns the preferences of the currently authenticated user.
    """
    logger.info(f"Preferences request for user ID: {current_user.id}")
    try:
        # Get the user's preferences
        preferences = db.query(UserPreferenceModel).filter(
            UserPreferenceModel.user_id == current_user.id
        ).first()

        # If preferences don't exist, create default preferences
        if not preferences:
            preferences = UserPreferenceModel(
                user_id=current_user.id,
                theme="light",
                language="en",
                notifications_enabled=True,
                email_notifications=True
            )
            db.add(preferences)
            db.commit()
            db.refresh(preferences)
            logger.info(
                f"Created default preferences for user ID: {current_user.id}")

        # Convert to response model
        return UserPreference(
            theme=preferences.theme,
            language=preferences.language,
            notifications_enabled=preferences.notifications_enabled,
            email_notifications=preferences.email_notifications
        )
    except Exception as e:
        logger.error(f"Error retrieving preferences: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving user preferences"
        )


@router.put("/preferences", response_model=UserPreference)
async def update_user_preferences(
    preferences_update: UserPreference,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Update current user's preferences

    Updates the preferences of the currently authenticated user.
    """
    logger.info(f"Preferences update for user ID: {current_user.id}")
    try:
        # Get existing preferences
        preferences = db.query(UserPreferenceModel).filter(
            UserPreferenceModel.user_id == current_user.id
        ).first()

        # If preferences don't exist, create new preferences
        if not preferences:
            preferences = UserPreferenceModel(
                user_id=current_user.id,
                theme=preferences_update.theme,
                language=preferences_update.language,
                notifications_enabled=preferences_update.notifications_enabled,
                email_notifications=preferences_update.email_notifications
            )
            db.add(preferences)
        else:
            # Update existing preferences
            preferences.theme = preferences_update.theme
            preferences.language = preferences_update.language
            preferences.notifications_enabled = preferences_update.notifications_enabled
            preferences.email_notifications = preferences_update.email_notifications
            preferences.updated_at = datetime.utcnow()

        # Save to database
        db.commit()
        db.refresh(preferences)

        # Log activity
        try:
            activity = UserActivityModel(
                user_id=current_user.id,
                action="preferences_update",
                details={"preferences_updated": True}
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.warning(
                f"Failed to log preferences update activity: {str(e)}")
            # Continue even if logging fails

        logger.info(
            f"Preferences updated successfully for user ID: {current_user.id}")

        # Convert to response model
        return UserPreference(
            theme=preferences.theme,
            language=preferences.language,
            notifications_enabled=preferences.notifications_enabled,
            email_notifications=preferences.email_notifications
        )
    except Exception as e:
        logger.error(f"Error updating preferences: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error updating user preferences"
        )

# User certifications endpoints


@router.get("/certifications", response_model=List[Dict[str, Any]])
async def get_user_certifications(
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get current user's certifications

    Returns the certifications of the currently authenticated user.
    """
    logger.info(f"Certifications request for user ID: {current_user.id}")
    try:
        # Query the user's certifications
        # Get all certifications the user has earned
        user_certifications = (
            db.query(
                UserCertification,
                Certification.name,
                Certification.code,
                Certification.organization
            )
            .join(
                Certification,
                UserCertification.certification_id == Certification.id
            )
            .filter(UserCertification.user_id == current_user.id)
            .all()
        )

        # Format the response
        result = []
        for uc, name, code, organization in user_certifications:
            result.append({
                "id": uc.id,
                "certification_id": uc.certification_id,
                "name": name,
                "code": code,
                "organization": organization,
                "date_earned": uc.date_earned,
                "expiration_date": uc.expiration_date,
                "verification_url": uc.verification_url,
                "certificate_image_url": uc.certificate_image_url,
                "status": uc.status
            })

        return result
    except Exception as e:
        logger.error(f"Error retrieving certifications: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error retrieving user certifications"
        )


@router.post("/certifications", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def add_user_certification(
    certification_data: Dict[str, Any],
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Add a certification to the current user

    Adds a new certification to the user's profile.
    """
    logger.info(f"Add certification request for user ID: {current_user.id}")
    try:
        # Validate certification existence
        certification_id = certification_data.get("certification_id")
        if not certification_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Certification ID is required"
            )

        # Check if certification exists
        certification = db.query(Certification).filter(
            Certification.id == certification_id
        ).first()

        if not certification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Certification not found"
            )

        # Check if user already has this certification
        existing = db.query(UserCertification).filter(
            UserCertification.user_id == current_user.id,
            UserCertification.certification_id == certification_id
        ).first()

        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="User already has this certification"
            )

        # Create new user certification
        user_certification = UserCertification(
            user_id=current_user.id,
            certification_id=certification_id,
            date_earned=certification_data.get("date_earned"),
            expiration_date=certification_data.get("expiration_date"),
            verification_url=certification_data.get("verification_url"),
            certificate_image_url=certification_data.get(
                "certificate_image_url"),
            status="active"
        )

        db.add(user_certification)
        db.commit()
        db.refresh(user_certification)

        # Log activity
        try:
            activity = UserActivityModel(
                user_id=current_user.id,
                action="certification_added",
                details={"certification_id": certification_id,
                         "certification_name": certification.name}
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.warning(
                f"Failed to log certification addition activity: {str(e)}")
            # Continue even if logging fails

        logger.info(
            f"Certification added successfully for user ID: {current_user.id}")

        # Return the added certification
        return {
            "id": user_certification.id,
            "certification_id": certification_id,
            "name": certification.name,
            "code": certification.code,
            "organization": certification.organization,
            "date_earned": user_certification.date_earned,
            "expiration_date": user_certification.expiration_date,
            "verification_url": user_certification.verification_url,
            "certificate_image_url": user_certification.certificate_image_url,
            "status": user_certification.status
        }
    except HTTPException:
        # Re-raise HTTP exceptions to preserve status code and details
        raise
    except Exception as e:
        logger.error(f"Error adding certification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error adding user certification"
        )


@router.delete("/certifications/{certification_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user_certification(
    certification_id: int,
    current_user=Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete a certification from the current user

    Removes a certification from the user's profile.
    """
    logger.info(
        f"Delete certification request for user ID: {current_user.id}, certification ID: {certification_id}")
    try:
        # Find the user certification
        user_certification = db.query(UserCertification).filter(
            UserCertification.user_id == current_user.id,
            UserCertification.id == certification_id
        ).first()

        if not user_certification:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User certification not found"
            )

        # Delete the certification
        db.delete(user_certification)
        db.commit()

        # Log activity
        try:
            activity = UserActivityModel(
                user_id=current_user.id,
                action="certification_deleted",
                details={"certification_id": user_certification.certification_id}
            )
            db.add(activity)
            db.commit()
        except Exception as e:
            logger.warning(
                f"Failed to log certification deletion activity: {str(e)}")
            # Continue even if logging fails

        logger.info(
            f"Certification deleted successfully for user ID: {current_user.id}")

        return None
    except HTTPException:
        # Re-raise HTTP exceptions to preserve status code and details
        raise
    except Exception as e:
        logger.error(f"Error deleting certification: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error deleting user certification"
        )
